# AUpload 组件 Binary 文件支持修复日志

## 修复概述
修复了 `src/components/aupload.vue` 组件，使其支持上传 binary 文件和多种文件类型。

## 修复时间
2025-07-26

## 主要修改内容

### 1. 新增 Props 参数
- `supportBinary` (Boolean): 是否支持binary文件上传，默认 false
- `acceptTypes` (Array): 允许的文件类型数组，默认为图片类型
- `maxSize` (Number): 最大文件大小限制(MB)，默认 10MB

### 2. 增强 beforeUpload 方法
- **原有问题**: 只支持 jpg/png/jpeg 图片格式，硬编码限制
- **修复方案**: 
  - 当 `supportBinary=true` 时，跳过文件类型检查
  - 支持通过 `acceptTypes` 自定义允许的文件类型
  - 支持通过 `maxSize` 自定义文件大小限制
  - 改进错误提示信息，显示具体的文件类型要求

### 3. 优化 handleUpload 方法
- **新增功能**:
  - 为 binary 文件添加额外标识字段 (`isBinary`, `fileType`)
  - 在文件列表中保存文件类型和大小信息
  - 增加错误处理回调，显示上传失败状态
- **改进点**:
  - 更清晰的代码结构
  - 更好的错误处理机制

### 4. 增强预览功能
- **模板改进**:
  - 图片文件显示预览图
  - 非图片文件显示文件图标、文件名、类型和下载链接
- **数据属性新增**:
  - `previewFileName`: 预览文件名
  - `previewFileType`: 预览文件类型
- **handlePreview 方法优化**:
  - 根据文件类型选择不同的预览方式
  - 支持非图片文件的信息展示

### 5. 代码质量改进
- 修复了 `removeFile` 方法中未使用参数的警告
- 改进了代码注释和结构
- 增强了错误处理机制

## 使用方式

### Binary 文件上传示例
```vue
<aupload 
  :action="'/api/common/fileUpload'"
  :func="'handleBinaryUpload'"
  :supportBinary="true"
  :maxSize="50"
  @handleBinaryUpload="onBinaryUpload"
/>
```

### 自定义文件类型示例
```vue
<aupload 
  :action="'/api/common/docUpload'"
  :func="'handleDocUpload'"
  :acceptTypes="['application/pdf', 'application/msword', 'text/plain']"
  :maxSize="20"
  @handleDocUpload="onDocUpload"
/>
```

## 服务端接口变更
当 `supportBinary=true` 时，FormData 中会包含额外字段：
- `isBinary`: 'true' - 标识binary文件
- `fileType`: 文件MIME类型

## 向后兼容性
- ✅ 完全向后兼容，原有的图片上传功能不受影响
- ✅ 默认参数保持不变，现有代码无需修改
- ✅ 新功能通过可选参数启用

## 测试建议
1. 测试原有图片上传功能是否正常
2. 测试 binary 文件上传功能
3. 测试自定义文件类型限制
4. 测试文件大小限制
5. 测试预览功能（图片和非图片文件）
6. 测试错误处理和提示信息

## 相关文件
- `src/components/aupload.vue` - 主要修复文件
- `src/components/aupload-usage-examples.md` - 使用说明文档
- `src/views/test-upload.vue` - 测试页面
- `AUPLOAD_BINARY_SUPPORT_CHANGELOG.md` - 本修复日志

## 技术细节
- 使用 FormData 支持二进制文件传输
- 通过 MIME 类型判断文件类型
- 使用 FileReader API 生成图片预览
- 保持与 Ant Design Vue Upload 组件的兼容性

## 风险评估
- 🟢 低风险：修改采用渐进式增强，不影响现有功能
- 🟢 向后兼容：所有现有代码无需修改
- 🟢 可选功能：新功能通过参数控制，默认关闭
