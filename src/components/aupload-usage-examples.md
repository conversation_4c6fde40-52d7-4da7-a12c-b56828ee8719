# AUpload 组件使用说明

## 概述
AUpload 组件已经修复并增强，现在支持上传 binary 文件和多种文件类型。

## 新增功能
1. **Binary 文件支持**: 支持上传任意二进制文件
2. **灵活的文件类型限制**: 可自定义允许的文件类型
3. **可配置的文件大小限制**: 可自定义最大文件大小
4. **增强的预览功能**: 图片文件显示预览，其他文件显示下载链接

## Props 参数

| 参数名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| func | String | '' | 父组件回调方法名 |
| sourceValue | String | '' | 初始文件值 |
| action | String | '' | 上传接口地址 |
| upName | String | 'upFile' | 上传字段名 |
| supportBinary | Boolean | false | 是否支持binary文件上传 |
| acceptTypes | Array | ['image/jpeg', 'image/png', 'image/jpg'] | 允许的文件类型 |
| maxSize | Number | 10 | 最大文件大小(MB) |

## 使用示例

### 1. 默认图片上传（原有功能）
```vue
<template>
  <aupload 
    :action="'/api/common/imgUpload'"
    :func="'handleImageUpload'"
    @handleImageUpload="onImageUpload"
  />
</template>

<script>
export default {
  methods: {
    onImageUpload(filePath) {
      console.log('上传的图片路径:', filePath);
    }
  }
}
</script>
```

### 2. Binary 文件上传
```vue
<template>
  <aupload 
    :action="'/api/common/fileUpload'"
    :func="'handleBinaryUpload'"
    :supportBinary="true"
    :maxSize="50"
    @handleBinaryUpload="onBinaryUpload"
  />
</template>

<script>
export default {
  methods: {
    onBinaryUpload(filePath) {
      console.log('上传的binary文件路径:', filePath);
    }
  }
}
</script>
```

### 3. 自定义文件类型限制
```vue
<template>
  <aupload 
    :action="'/api/common/docUpload'"
    :func="'handleDocUpload'"
    :acceptTypes="['application/pdf', 'application/msword', 'text/plain']"
    :maxSize="20"
    @handleDocUpload="onDocUpload"
  />
</template>

<script>
export default {
  methods: {
    onDocUpload(filePath) {
      console.log('上传的文档路径:', filePath);
    }
  }
}
</script>
```

### 4. 视频文件上传
```vue
<template>
  <aupload 
    :action="'/api/common/videoUpload'"
    :func="'handleVideoUpload'"
    :acceptTypes="['video/mp4', 'video/avi', 'video/mov']"
    :maxSize="100"
    @handleVideoUpload="onVideoUpload"
  />
</template>

<script>
export default {
  methods: {
    onVideoUpload(filePath) {
      console.log('上传的视频路径:', filePath);
    }
  }
}
</script>
```

## 服务端接口说明

当 `supportBinary` 为 `true` 时，组件会在 FormData 中添加额外的字段：
- `isBinary`: 'true' - 标识这是一个binary文件上传
- `fileType`: 文件的MIME类型，如 'application/octet-stream'

服务端可以根据这些字段来特殊处理binary文件。

## 注意事项

1. **文件类型检查**: 当 `supportBinary` 为 `true` 时，会跳过文件类型检查
2. **文件大小限制**: 所有文件都会检查大小限制
3. **预览功能**: 图片文件会显示预览图，其他文件会显示下载链接
4. **错误处理**: 上传失败时会显示错误状态和提示信息

## 常见文件类型 MIME 对照表

| 文件类型 | MIME 类型 |
|----------|-----------|
| PDF | application/pdf |
| Word文档 | application/msword |
| Excel表格 | application/vnd.ms-excel |
| PowerPoint | application/vnd.ms-powerpoint |
| 文本文件 | text/plain |
| JSON文件 | application/json |
| ZIP压缩包 | application/zip |
| MP4视频 | video/mp4 |
| MP3音频 | audio/mpeg |
| 任意二进制 | application/octet-stream |
