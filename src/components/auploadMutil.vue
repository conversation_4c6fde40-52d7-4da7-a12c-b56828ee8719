
<template>
  <div class="clearfix">
    <a-upload
      :action="action"
      list-type="picture-card"
      :file-list="fileList"
      @preview="handlePreview"
      :name="upName"
      :headers="headers"
      :before-upload="beforeUpload"
      :customRequest="handleUpload"
      @change="handleChange"
      
    >
      <div v-if="fileList.length < uploadLength">
        <a-icon type="plus" />
        <div class="ant-upload-text">
          Upload
        </div>
      </div>
    </a-upload>
    <a-modal :visible="previewVisible" :footer="null" @cancel="handleCancel" :zIndex="99999">
      <div v-if="previewFileType && previewFileType.startsWith('image/')">
        <img alt="example" style="width: 100%" :src="previewImage" />
      </div>
      <div v-else style="text-align: center; padding: 20px;">
        <a-icon type="file" style="font-size: 48px; color: #999;" />
        <p style="margin-top: 10px;">{{ previewFileName }}</p>
        <p style="color: #999; font-size: 12px;">{{ previewFileType || 'Binary File' }}</p>
        <a :href="previewImage" :download="previewFileName" target="_blank">
          <a-button type="primary" size="small">下载文件</a-button>
        </a>
      </div>
    </a-modal>
  </div>
</template>

<script>
function getBase64(file) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result);
    reader.onerror = error => reject(error);
  });
}
export default {
  name: 'auploadMutil',
  props: {
    //父类调用方法
    func:{
          type:String,
          default:''
    },
    //来源值
    sourceValue:{
        type:Array|String,
        default:[]
    },
    uploadLength:{
      type:Number,
      default:1
    },
    // 上传接口地址
    url: {
      type: String,
      default: '/api/common/imgUpload'
    },
    // 上传字段名
    upName: {
      type: String,
      default: 'upFile'
    },
    // 是否支持binary文件上传
    supportBinary: {
      type: Boolean,
      default: false
    },
    // 允许的文件类型，为空则不限制
    acceptTypes: {
      type: Array,
      default: () => ['image/jpeg', 'image/png', 'image/jpg']
    },
    // 最大文件大小（MB）
    maxSize: {
      type: Number,
      default: 10
    }
  },
  data() {
    return {
      action: this.$utils.getHttp() + this.url, //请求地址
      headers: {
         'token' : this.$utils.store.getS('token')
      },            //header
      previewVisible: false,
      previewImage: '',
      previewFileName: '',
      previewFileType: '',
      fileList: [
        // {
        //   uid: '-1',
        //   name: 'image.png',
        //   status: 'done',
        //   url: 'https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png',
        // },
      ],
      fileNum : 0
    };
  },
  watch:{
    
  },
  mounted:function () {
    let self = this;

    if(self.sourceValue != ''){
      //let jsonData = JSON.parse(self.sourceValue);

      let fileList = [];
      self.sourceValue.forEach((value, index) => {
          fileList.push({
              uid: index,
              name: value,
              status: 'done',
              url: self.$utils.getHttp()+value
            })
      });
      
      self.fileNum  = self.sourceValue.length;
      self.fileList = fileList;
    }
  },
  methods: {
    handleCancel() {
      this.previewVisible = false;
    },
    async handlePreview(file) {
      // 设置预览文件信息
      this.previewFileName = file.name;
      this.previewFileType = file.type;

      // 如果是图片文件，生成预览
      if (file.type && file.type.startsWith('image/')) {
        if (!file.url && !file.preview) {
          file.preview = await getBase64(file.originFileObj);
        }
        this.previewImage = file.url || file.preview;
      } else {
        // 对于非图片文件，使用文件URL
        this.previewImage = file.url;
      }

      this.previewVisible = true;
    },
    handleChange(info) {
      if (info.file.status === 'uploading') {
        this.loading = true;
        return;
      }
      if (info.file.status === 'done') {
        // Get this url from response in real world.
        getBase64(info.file.originFileObj, imageUrl => {
          this.imageUrl = imageUrl;
          this.loading = false;
        });
      }
    },

    // handleChange({ fileList }) {
    //   this.fileList = fileList;
    // },

    beforeUpload(file) {
      // 如果支持binary文件，则跳过文件类型检查
      if (this.supportBinary) {
        const isLtMaxSize = file.size / 1024 / 1024 < this.maxSize;
        if (!isLtMaxSize) {
          this.$message.error(`文件上传最大限制 ${this.maxSize}M !`);
        }
        return isLtMaxSize;
      }

      // 检查文件类型
      let isValidType = true;
      if (this.acceptTypes && this.acceptTypes.length > 0) {
        isValidType = this.acceptTypes.includes(file.type);
        if (!isValidType) {
          const typeNames = this.acceptTypes.map(type => {
            if (type.includes('image/')) {
              return type.replace('image/', '');
            }
            return type;
          }).join(' 或 ');
          this.$message.error(`请上传 ${typeNames} 格式的文件`);
        }
      }

      // 检查文件大小
      const isLtMaxSize = file.size / 1024 / 1024 < this.maxSize;
      if (!isLtMaxSize) {
        this.$message.error(`文件上传最大限制 ${this.maxSize}M !`);
      }

      return isValidType && isLtMaxSize;
    },

    handleUpload(option) {
      let self = this;

      // 创建FormData对象
      let params = new FormData();
      params.append(self.upName, option.file);

      // 如果是binary文件，添加额外的标识
      if (self.supportBinary) {
        params.append('isBinary', 'true');
        params.append('fileType', option.file.type || 'application/octet-stream');
      }

      // 调试FormData内容
      console.log('上传文件信息:', {
        fileName: option.file.name,
        fileSize: option.file.size,
        fileType: option.file.type,
        upName: self.upName,
        supportBinary: self.supportBinary
      });

      console.log('FormData内容:');
      for (let pair of params.entries()) {
        if (pair[1] instanceof File) {
          console.log(`  ${pair[0]}: [File] ${pair[1].name} (${pair[1].size} bytes)`);
        } else {
          console.log(`  ${pair[0]}: ${pair[1]}`);
        }
      }

      this.$utils.http({
            data: params,
            headers: {
              'token': self.$utils.store.getS('token'),
              'Content-Type': 'multipart/form-data',
            },
            success: (res) => {
              let data = res.data;
              self.$message.success(res.message);

              let fileList = {
                  uid: self.fileNum,
                  name: option.file.name,
                  status: 'done',
                  url: data.httpFile,
                  type: option.file.type,
                  size: option.file.size,
                };

              self.fileList.push(fileList);
              self.$emit(self.func, data.originFile);
              self.fileNum++;
            },
            fail: (error) => {
              console.error('Upload failed:', error);
              self.$message.error('上传失败，请重试');

              // 更新文件状态为错误
              let fileList = {
                  uid: self.fileNum,
                  name: option.file.name,
                  status: 'error',
                };
              self.fileList.push(fileList);
              self.fileNum++;
            }
          }, self.url)
    },
  },
};
</script>

<style scoped>
/* you can make up upload button and sample style by using stylesheets */
.ant-upload-select-picture-card i {
  font-size: 32px;
  color: #999;
}

.ant-upload-select-picture-card .ant-upload-text {
  margin-top: 8px;
  color: #666;
}
</style>