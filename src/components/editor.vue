<template>
    <div>
        <div ref="editor" style="text-align:left"></div>
    </div>
</template>

<script>
    import E from 'wangeditor'
    

    export default {
      name: 'editor',
      data () {
        return {
          editorContent: '',
          editor: null
        }
      },
      props: {
        //父类调用方法
        func:{
          type:String,
          default:''
        },
        //来源值
        sourceValue:{
          type:String,
          default:''
        },
        imgUploadUrl: {
          type: String,
          default: '/api/common/imgUpload'
        },
        imgUploadName: {
          type: String,
          default: 'upFile'
        },
        // 小程序展示配置
        miniProgramMode: {
          type: Boolean,
          default: false
        },
        // 小程序图片域名配置
        miniProgramImageDomain: {
          type: String,
          default: ''
        },
        // 小程序不支持的标签替换配置
        miniProgramTagReplace: {
          type: Object,
          default: () => ({
            'video': 'image',  // 视频标签替换为图片
            'audio': 'text',   // 音频标签替换为文本
            'iframe': 'text'   // iframe替换为文本
          })
        }
      },
      methods: {
        getContent: function () {

        },

        // 处理小程序展示内容
        processMiniProgramContent: function(html) {
          if (!this.miniProgramMode || !html) {
            return html;
          }

          let processedHtml = html;

          // 1. 处理图片域名
          if (this.miniProgramImageDomain) {
            // 替换图片src为小程序可访问的域名
            processedHtml = processedHtml.replace(
              /<img([^>]*?)src=["']([^"']*?)["']/g,
              (match, attrs, src) => {
                // 如果是相对路径或当前域名，替换为小程序域名
                if (src.startsWith('/') || src.startsWith('./') || src.includes(window.location.host)) {
                  const newSrc = src.startsWith('/') ?
                    this.miniProgramImageDomain + src :
                    this.miniProgramImageDomain + '/' + src.replace(/^\.\//, '');
                  return `<img${attrs}src="${newSrc}"`;
                }
                return match;
              }
            );
          }

          // 2. 处理不支持的标签
          Object.keys(this.miniProgramTagReplace).forEach(tag => {
            const replacement = this.miniProgramTagReplace[tag];
            const tagRegex = new RegExp(`<${tag}[^>]*>.*?<\/${tag}>`, 'gi');

            if (replacement === 'text') {
              // 替换为纯文本
              processedHtml = processedHtml.replace(tagRegex, (match) => {
                const textContent = match.replace(/<[^>]*>/g, '').trim();
                return textContent ? `<p>[${tag.toUpperCase()}内容]: ${textContent}</p>` : '';
              });
            } else if (replacement === 'image') {
              // 替换为占位图片
              processedHtml = processedHtml.replace(tagRegex,
                `<p><img src="${this.miniProgramImageDomain || ''}/placeholder/${tag}.png" alt="${tag}内容" style="max-width:100%;height:auto;" /></p>`
              );
            }
          });

          // 3. 处理样式兼容性
          processedHtml = this.processMiniProgramStyles(processedHtml);

          return processedHtml;
        },

        // 处理小程序样式兼容性
        processMiniProgramStyles: function(html) {
          // 移除小程序不支持的CSS属性
          const unsupportedStyles = [
            'position\\s*:\\s*fixed',
            'position\\s*:\\s*sticky',
            'transform\\s*:',
            'animation\\s*:',
            'transition\\s*:'
          ];

          let processedHtml = html;

          unsupportedStyles.forEach(stylePattern => {
            const regex = new RegExp(stylePattern + '[^;]*;?', 'gi');
            processedHtml = processedHtml.replace(regex, '');
          });

          // 清理空的style属性
          processedHtml = processedHtml.replace(/style=["']\s*["']/g, '');

          return processedHtml;
        },

        // 获取小程序适配后的内容
        getMiniProgramContent: function() {
          const editorContent = this.editorContent || '';
          return this.processMiniProgramContent(editorContent);
        }
      },
      mounted() {
        let self   = this;
        let editor = new E(this.$refs.editor)
        
        editor.customConfig.onchange = (html) => {
          // 存储原始内容
          self.editorContent = html;

          // 如果启用小程序模式，同时返回处理后的内容
          if (self.miniProgramMode) {
            const miniProgramHtml = self.processMiniProgramContent(html);
            self.$emit(self.func, html, miniProgramHtml);
          } else {
            self.$emit(self.func, html);
          }
        }

        // 配置服务器端地址
         editor.customConfig.uploadImgServer = self.$utils.getHttp()+self.imgUploadUrl
         editor.customConfig.uploadFileName = self.imgUploadName
         editor.customConfig.uploadImgMaxSize = 10 * 1024 * 1024 // 10MB
         editor.customConfig.uploadImgAccept = ['jpg', 'jpeg', 'png', 'gif'] // 允许的图片格式
         editor.customConfig.uploadImgMaxLength = 5 // 最多上传5张图片

         editor.customConfig.uploadImgHeaders = {
            'token'        : self.$utils.store.getS('token')
        }

          // 自定义菜单配置
        editor.customConfig.menus = [
            'head',  // 标题
            'bold',  // 粗体
            'fontSize',  // 字号
            'fontName',  // 字体
            'italic',  // 斜体
            'underline',  // 下划线
            'strikeThrough',  // 删除线
            'foreColor',  // 文字颜色
            'backColor',  // 背景颜色
            'link',  // 插入链接
            'list',  // 列表
            'justify',  // 对齐方式
            'quote',  // 引用
            'emoticon',  // 表情
            'image',  // 插入图片
            'table',  // 表格
            //'video',  // 插入视频
            //'code',  // 插入代码
            'undo',  // 撤销
            'redo'  // 重复
        ]

        editor.customConfig.uploadImgHooks = {
          before: function (xhr, editor, files) {
              // 图片上传之前触发
              // xhr 是 XMLHttpRequst 对象，editor 是编辑器对象，files 是选择的图片文件
              
              // 如果返回的结果是 {prevent: true, msg: 'xxxx'} 则表示用户放弃上传
              // return {
              //     prevent: true,
              //     msg: '放弃上传'
              // }
              self.$message.loading('loading', 2.5);
          },
          success: function (xhr, editor, result) {
              // 图片上传并返回结果，图片插入成功之后触发
              // xhr 是 XMLHttpRequst 对象，editor 是编辑器对象，result 是服务器端返回的结果
               self.$message.destroy();
               self.$message.success(result.msg || result.message || '上传成功');

          },
          fail: function (xhr, editor, result) {
              // 图片上传并返回结果，但图片插入错误时触发
              // xhr 是 XMLHttpRequst 对象，editor 是编辑器对象，result 是服务器端返回的结果
              self.$message.destroy();
              self.$message.error(result.msg || result.message || '上传失败')
          },
          error: function (xhr, editor, result) {
              // 图片上传出错时触发
              // xhr 是 XMLHttpRequst 对象，editor 是编辑器对象
              let response = JSON.parse(xhr.response);
              self.$message.destroy();
              self.$message.error(response.msg || response.message || '上传出错')
          },
          timeout: function (xhr, editor) {
              // 图片上传超时时触发
              // xhr 是 XMLHttpRequst 对象，editor 是编辑器对象
          },

          // 如果服务器端返回的不是 {errno:0, data: [...]} 这种格式，可使用该配置
          // （但是，服务器端返回的必须是一个 JSON 格式字符串！！！否则会报错）
          customInsert: function (insertImg, result, editor) {
              // 图片上传并返回结果，自定义插入图片的事件（而不是编辑器自动插入图片！！！）
              // insertImg 是插入图片的函数，editor 是编辑器对象，result 是服务器端返回的结果

              console.log('Editor upload result:', result);

              // 适配新的接口响应格式
              let data = result.data;
              let uploadedFile = Array.isArray(data) ? data[0] : data;

              // 构建完整的图片URL
              let imageUrl;
              if (uploadedFile.fullPath) {
                  // 新接口格式：使用 fullPath
                  imageUrl = self.$utils.getHttp() + '/' + uploadedFile.fullPath;
              } else if (uploadedFile.httpFile) {
                  // 兼容旧接口格式：使用 httpFile
                  imageUrl = uploadedFile.httpFile;
              } else {
                  console.error('无法获取图片URL，响应数据:', result);
                  return;
              }

              console.log('插入图片URL:', imageUrl);
              insertImg(imageUrl);

              // result 必须是一个 JSON 格式字符串！！！否则报错
          }
      }

      editor.customConfig.customAlert = function () {
          // 自定义alert
      }
      

        // 进行下文提到的其他配置
        editor.create()

        // 保存编辑器实例
        self.editor = editor;

        if(self.sourceValue != ''){
            editor.txt.html(self.sourceValue)
            // 初始化时也处理小程序内容
            self.editorContent = self.sourceValue;
        }
      }
    }
</script>

<style scoped>
</style>
