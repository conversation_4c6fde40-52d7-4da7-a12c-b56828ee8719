<template>
  <div style="padding: 20px;">
    <h1>所有上传组件测试页面</h1>
    
    <div style="margin-bottom: 40px;">
      <h2>1. aupload - 单文件上传组件</h2>
      
      <div style="margin-bottom: 20px;">
        <h3>1.1 默认图片上传</h3>
        <aupload 
          :url="'/api/common/imgUpload'"
          :func="'handleImageUpload'"
          @handleImageUpload="onImageUpload"
        />
        <p>结果: {{ imageResult }}</p>
      </div>

      <div style="margin-bottom: 20px;">
        <h3>1.2 Binary 文件上传</h3>
        <aupload 
          :url="'/api/common/fileUpload'"
          :func="'handleBinaryUpload'"
          :supportBinary="true"
          :maxSize="50"
          @handleBinaryUpload="onBinaryUpload"
        />
        <p>结果: {{ binaryResult }}</p>
      </div>

      <div style="margin-bottom: 20px;">
        <h3>1.3 PDF 文档上传</h3>
        <aupload 
          :url="'/api/common/docUpload'"
          :func="'handlePdfUpload'"
          :acceptTypes="['application/pdf']"
          :maxSize="20"
          @handlePdfUpload="onPdfUpload"
        />
        <p>结果: {{ pdfResult }}</p>
      </div>
    </div>

    <div style="margin-bottom: 40px;">
      <h2>2. auploadMutil - 卡片式多文件上传组件</h2>
      
      <div style="margin-bottom: 20px;">
        <h3>2.1 多图片上传</h3>
        <auploadMutil 
          :url="'/api/common/imgUpload'"
          :func="'handleMultiImageUpload'"
          :uploadLength="3"
          @handleMultiImageUpload="onMultiImageUpload"
        />
        <p>结果: {{ multiImageResults.join(', ') }}</p>
      </div>

      <div style="margin-bottom: 20px;">
        <h3>2.2 多 Binary 文件上传</h3>
        <auploadMutil 
          :url="'/api/common/fileUpload'"
          :func="'handleMultiBinaryUpload'"
          :supportBinary="true"
          :uploadLength="5"
          :maxSize="30"
          @handleMultiBinaryUpload="onMultiBinaryUpload"
        />
        <p>结果: {{ multiBinaryResults.join(', ') }}</p>
      </div>

      <div style="margin-bottom: 20px;">
        <h3>2.3 视频文件上传</h3>
        <auploadMutil 
          :url="'/api/common/videoUpload'"
          :func="'handleVideoUpload'"
          :acceptTypes="['video/mp4', 'video/avi', 'video/mov']"
          :uploadLength="2"
          :maxSize="100"
          @handleVideoUpload="onVideoUpload"
        />
        <p>结果: {{ videoResults.join(', ') }}</p>
      </div>
    </div>

    <div style="margin-bottom: 40px;">
      <h2>3. auploadList - 列表式多文件上传组件</h2>
      
      <div style="margin-bottom: 20px;">
        <h3>3.1 列表式图片上传</h3>
        <auploadList 
          :url="'/api/common/imgUpload'"
          :func="'handleListImageUpload'"
          :showImgInline="false"
          :infoImgUploadVisible="listUploadVisible"
          :infoImgUploadSaveStatus="listSaveStatus"
          @handleListImageUpload="onListImageUpload"
        />
        <div style="margin-top: 10px;">
          <a-button @click="toggleListUpload">{{ listUploadVisible ? '隐藏' : '显示' }}上传</a-button>
          <a-button @click="saveListUpload" style="margin-left: 10px;">保存上传</a-button>
        </div>
        <p>结果: {{ JSON.stringify(listImageResults) }}</p>
      </div>

      <div style="margin-bottom: 20px;">
        <h3>3.2 列表式 Binary 文件上传</h3>
        <auploadList 
          :url="'/api/common/fileUpload'"
          :func="'handleListBinaryUpload'"
          :supportBinary="true"
          :showImgInline="true"
          :maxSize="50"
          :infoImgUploadVisible="listBinaryUploadVisible"
          :infoImgUploadSaveStatus="listBinarySaveStatus"
          @handleListBinaryUpload="onListBinaryUpload"
        />
        <div style="margin-top: 10px;">
          <a-button @click="toggleListBinaryUpload">{{ listBinaryUploadVisible ? '隐藏' : '显示' }}上传</a-button>
          <a-button @click="saveListBinaryUpload" style="margin-left: 10px;">保存上传</a-button>
        </div>
        <p>结果: {{ JSON.stringify(listBinaryResults) }}</p>
      </div>
    </div>

    <div style="margin-top: 40px; padding: 20px; background: #f5f5f5; border-radius: 8px;">
      <h3>测试说明</h3>
      <ul>
        <li>所有组件都支持 binary 文件上传（通过 supportBinary 参数）</li>
        <li>可以通过 acceptTypes 自定义允许的文件类型</li>
        <li>可以通过 maxSize 自定义文件大小限制</li>
        <li>图片文件会显示预览，其他文件显示下载链接</li>
        <li>查看浏览器控制台可以看到详细的调试信息</li>
      </ul>
    </div>
  </div>
</template>

<script>
import aupload from '@/components/aupload.vue'
import auploadMutil from '@/components/auploadMutil.vue'
import auploadList from '@/components/auploadList.vue'

export default {
  name: 'TestAllUploads',
  components: {
    aupload,
    auploadMutil,
    auploadList
  },
  data() {
    return {
      // 单文件上传结果
      imageResult: '',
      binaryResult: '',
      pdfResult: '',
      
      // 多文件上传结果
      multiImageResults: [],
      multiBinaryResults: [],
      videoResults: [],
      
      // 列表上传状态和结果
      listUploadVisible: false,
      listSaveStatus: false,
      listImageResults: [],
      
      listBinaryUploadVisible: false,
      listBinarySaveStatus: false,
      listBinaryResults: []
    }
  },
  methods: {
    // 单文件上传回调
    onImageUpload(filePath) {
      console.log('图片上传成功:', filePath);
      this.imageResult = filePath;
      this.$message.success('图片上传成功');
    },
    
    onBinaryUpload(filePath) {
      console.log('Binary文件上传成功:', filePath);
      this.binaryResult = filePath;
      this.$message.success('Binary文件上传成功');
    },
    
    onPdfUpload(filePath) {
      console.log('PDF上传成功:', filePath);
      this.pdfResult = filePath;
      this.$message.success('PDF上传成功');
    },
    
    // 多文件上传回调
    onMultiImageUpload(filePath) {
      console.log('多图片上传成功:', filePath);
      this.multiImageResults.push(filePath);
      this.$message.success('多图片上传成功');
    },
    
    onMultiBinaryUpload(filePath) {
      console.log('多Binary文件上传成功:', filePath);
      this.multiBinaryResults.push(filePath);
      this.$message.success('多Binary文件上传成功');
    },
    
    onVideoUpload(filePath) {
      console.log('视频上传成功:', filePath);
      this.videoResults.push(filePath);
      this.$message.success('视频上传成功');
    },
    
    // 列表上传回调和控制
    onListImageUpload(filePath) {
      console.log('列表图片上传完成:', filePath);
      this.listImageResults.push(filePath);
      this.$message.success('列表图片上传成功');
    },

    onListBinaryUpload(filePath) {
      console.log('列表Binary文件上传完成:', filePath);
      this.listBinaryResults.push(filePath);
      this.$message.success('列表Binary文件上传成功');
    },
    
    toggleListUpload() {
      this.listUploadVisible = !this.listUploadVisible;
    },
    
    saveListUpload() {
      this.listSaveStatus = true;
      setTimeout(() => {
        this.listSaveStatus = false;
      }, 100);
    },
    
    toggleListBinaryUpload() {
      this.listBinaryUploadVisible = !this.listBinaryUploadVisible;
    },
    
    saveListBinaryUpload() {
      this.listBinarySaveStatus = true;
      setTimeout(() => {
        this.listBinarySaveStatus = false;
      }, 100);
    }
  }
}
</script>

<style scoped>
h1 {
  color: #1890ff;
  border-bottom: 3px solid #f0f0f0;
  padding-bottom: 15px;
}

h2 {
  color: #333;
  border-bottom: 2px solid #f0f0f0;
  padding-bottom: 10px;
  margin-top: 30px;
}

h3 {
  color: #666;
  margin-bottom: 15px;
}

p {
  color: #666;
  font-size: 14px;
  margin-top: 10px;
  padding: 8px;
  background-color: #f9f9f9;
  border-radius: 4px;
  min-height: 20px;
  word-break: break-all;
}

ul {
  margin: 0;
  padding-left: 20px;
}

li {
  margin-bottom: 5px;
  color: #666;
}
</style>
