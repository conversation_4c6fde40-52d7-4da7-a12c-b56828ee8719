<template>
  <div style="padding: 20px;">
    <h2>AUpload 组件测试页面</h2>
    
    <div style="margin-bottom: 30px;">
      <h3>1. 默认图片上传</h3>
      <aupload 
        :action="'/api/common/imgUpload'"
        :func="'handleImageUpload'"
        @handleImageUpload="onImageUpload"
      />
      <p>上传结果: {{ imageResult }}</p>
    </div>

    <div style="margin-bottom: 30px;">
      <h3>2. Binary 文件上传（支持任意文件类型）</h3>
      <aupload 
        :action="'/api/common/fileUpload'"
        :func="'handleBinaryUpload'"
        :supportBinary="true"
        :maxSize="50"
        @handleBinaryUpload="onBinaryUpload"
      />
      <p>上传结果: {{ binaryResult }}</p>
    </div>

    <div style="margin-bottom: 30px;">
      <h3>3. 文档文件上传（PDF, Word, 文本）</h3>
      <aupload 
        :action="'/api/common/docUpload'"
        :func="'handleDocUpload'"
        :acceptTypes="['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'text/plain']"
        :maxSize="20"
        @handleDocUpload="onDocUpload"
      />
      <p>上传结果: {{ docResult }}</p>
    </div>

    <div style="margin-bottom: 30px;">
      <h3>4. 视频文件上传</h3>
      <aupload 
        :action="'/api/common/videoUpload'"
        :func="'handleVideoUpload'"
        :acceptTypes="['video/mp4', 'video/avi', 'video/mov', 'video/quicktime']"
        :maxSize="100"
        @handleVideoUpload="onVideoUpload"
      />
      <p>上传结果: {{ videoResult }}</p>
    </div>

    <div style="margin-bottom: 30px;">
      <h3>5. 音频文件上传</h3>
      <aupload 
        :action="'/api/common/audioUpload'"
        :func="'handleAudioUpload'"
        :acceptTypes="['audio/mpeg', 'audio/wav', 'audio/ogg']"
        :maxSize="30"
        @handleAudioUpload="onAudioUpload"
      />
      <p>上传结果: {{ audioResult }}</p>
    </div>
  </div>
</template>

<script>
import aupload from '@/components/aupload.vue'

export default {
  name: 'TestUpload',
  components: {
    aupload
  },
  data() {
    return {
      imageResult: '',
      binaryResult: '',
      docResult: '',
      videoResult: '',
      audioResult: ''
    }
  },
  methods: {
    onImageUpload(filePath) {
      console.log('图片上传成功:', filePath);
      this.imageResult = filePath;
      this.$message.success('图片上传成功');
    },
    
    onBinaryUpload(filePath) {
      console.log('Binary文件上传成功:', filePath);
      this.binaryResult = filePath;
      this.$message.success('Binary文件上传成功');
    },
    
    onDocUpload(filePath) {
      console.log('文档上传成功:', filePath);
      this.docResult = filePath;
      this.$message.success('文档上传成功');
    },
    
    onVideoUpload(filePath) {
      console.log('视频上传成功:', filePath);
      this.videoResult = filePath;
      this.$message.success('视频上传成功');
    },
    
    onAudioUpload(filePath) {
      console.log('音频上传成功:', filePath);
      this.audioResult = filePath;
      this.$message.success('音频上传成功');
    }
  }
}
</script>

<style scoped>
h2 {
  color: #1890ff;
  border-bottom: 2px solid #f0f0f0;
  padding-bottom: 10px;
}

h3 {
  color: #333;
  margin-bottom: 15px;
}

p {
  color: #666;
  font-size: 14px;
  margin-top: 10px;
  padding: 8px;
  background-color: #f9f9f9;
  border-radius: 4px;
  min-height: 20px;
}
</style>
