<template>
  <div style="padding: 20px;">
    <h1>富文本编辑器测试页面</h1>
    
    <div style="margin-bottom: 20px;">
      <h2>编辑器功能测试</h2>
      <p>请在编辑器中测试以下功能：</p>
      <ul>
        <li>✅ 文本编辑功能</li>
        <li>✅ 图片上传功能（点击图片按钮上传）</li>
        <li>✅ 各种格式化功能</li>
      </ul>
    </div>

    <div style="border: 1px solid #d9d9d9; border-radius: 6px; padding: 20px; margin-bottom: 20px;">
      <h3>富文本编辑器</h3>
      <editor 
        :func="'handleEditorChange'"
        :sourceValue="initialContent"
        @handleEditorChange="onEditorChange"
      />
    </div>

    <div style="margin-top: 20px;">
      <h3>编辑器内容预览</h3>
      <div style="border: 1px solid #f0f0f0; padding: 15px; border-radius: 4px; background: #fafafa;">
        <div v-html="editorContent" style="min-height: 100px;"></div>
      </div>
    </div>

    <div style="margin-top: 20px;">
      <h3>编辑器HTML源码</h3>
      <pre style="background: #f5f5f5; padding: 15px; border-radius: 4px; overflow-x: auto; font-size: 12px;">{{ editorContent || '暂无内容' }}</pre>
    </div>

    <div style="margin-top: 30px; padding: 20px; background: #e6f7ff; border-radius: 8px; border: 1px solid #91d5ff;">
      <h3>📋 测试说明</h3>
      <div style="color: #666;">
        <h4>图片上传测试步骤：</h4>
        <ol>
          <li>点击编辑器工具栏中的图片按钮 📷</li>
          <li>选择要上传的图片文件</li>
          <li>等待上传完成</li>
          <li>图片会自动插入到编辑器中</li>
          <li>查看控制台日志了解上传详情</li>
        </ol>
        
        <h4>接口适配说明：</h4>
        <ul>
          <li>✅ 支持新的数组格式响应：<code>{"code": 200, "msg": "成功", "data": [...]}</code></li>
          <li>✅ 自动适配新旧接口格式</li>
          <li>✅ 使用 <code>fullPath</code> 构建完整图片URL</li>
          <li>✅ 兼容旧的 <code>httpFile</code> 字段</li>
          <li>✅ 消息字段支持 <code>msg</code> 和 <code>message</code></li>
        </ul>

        <h4>调试信息：</h4>
        <p>打开浏览器控制台可以看到：</p>
        <ul>
          <li>上传响应数据详情</li>
          <li>构建的图片URL</li>
          <li>编辑器内容变化</li>
        </ul>
      </div>
    </div>

    <div style="margin-top: 20px;">
      <h3>操作按钮</h3>
      <a-button @click="clearEditor" style="margin-right: 10px;">清空编辑器</a-button>
      <a-button @click="setInitialContent" type="primary" style="margin-right: 10px;">设置初始内容</a-button>
      <a-button @click="getEditorContent" type="dashed">获取编辑器内容</a-button>
    </div>
  </div>
</template>

<script>
import editor from '@/components/editor.vue'

export default {
  name: 'TestEditor',
  components: {
    editor
  },
  data() {
    return {
      editorContent: '',
      initialContent: `
        <h2>欢迎使用富文本编辑器</h2>
        <p>这是一个测试页面，用于验证编辑器的各项功能。</p>
        <p><strong>请测试以下功能：</strong></p>
        <ul>
          <li>文本格式化（加粗、斜体、颜色等）</li>
          <li>图片上传功能</li>
          <li>链接插入</li>
          <li>列表功能</li>
        </ul>
        <p><em>开始编辑吧！</em></p>
      `
    }
  },
  methods: {
    onEditorChange(html) {
      console.log('编辑器内容变化:', html);
      this.editorContent = html;
    },
    
    clearEditor() {
      this.editorContent = '';
      this.initialContent = '';
      this.$message.info('编辑器已清空，请刷新页面重新加载');
    },
    
    setInitialContent() {
      this.initialContent = `
        <h2>重新设置的内容</h2>
        <p>这是通过按钮重新设置的初始内容。</p>
        <p style="color: red;">红色文字测试</p>
        <p style="background-color: yellow;">黄色背景测试</p>
      `;
      this.$message.success('初始内容已设置，请刷新页面查看效果');
    },
    
    getEditorContent() {
      if (this.editorContent) {
        console.log('当前编辑器内容:', this.editorContent);
        this.$message.success('编辑器内容已输出到控制台');
        
        // 可以在这里处理编辑器内容，比如保存到服务器
        // this.saveContent(this.editorContent);
      } else {
        this.$message.warning('编辑器内容为空');
      }
    }
  },
  
  mounted() {
    console.log('编辑器测试页面已加载');
    console.log('初始内容:', this.initialContent);
  }
}
</script>

<style scoped>
h1 {
  color: #1890ff;
  border-bottom: 3px solid #f0f0f0;
  padding-bottom: 15px;
}

h2 {
  color: #333;
  border-bottom: 2px solid #f0f0f0;
  padding-bottom: 10px;
  margin-top: 30px;
}

h3 {
  color: #666;
  margin-bottom: 15px;
}

h4 {
  color: #333;
  margin: 15px 0 10px 0;
}

ul, ol {
  margin: 10px 0;
  padding-left: 25px;
}

li {
  margin-bottom: 5px;
  color: #666;
}

code {
  background: #f5f5f5;
  padding: 2px 6px;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
  color: #d63384;
}

pre {
  white-space: pre-wrap;
  word-wrap: break-word;
  max-height: 300px;
  overflow-y: auto;
}
</style>
