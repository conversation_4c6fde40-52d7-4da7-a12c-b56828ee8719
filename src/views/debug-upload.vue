<template>
  <div style="padding: 20px;">
    <h2>FormData 调试页面</h2>
    
    <div style="margin-bottom: 30px;">
      <h3>测试 Binary 文件上传</h3>
      <aupload 
        :url="'/api/common/fileUpload'"
        :func="'handleDebugUpload'"
        :supportBinary="true"
        :maxSize="50"
        @handleDebugUpload="onDebugUpload"
      />
      <p>上传结果: {{ uploadResult }}</p>
    </div>

    <div style="margin-bottom: 30px;">
      <h3>手动测试 FormData</h3>
      <input type="file" @change="testFormData" />
      <button @click="sendTestData" :disabled="!testFile">发送测试数据</button>
    </div>

    <div style="margin-bottom: 30px;">
      <h3>调试信息</h3>
      <pre style="background: #f5f5f5; padding: 10px; border-radius: 4px;">{{ debugInfo }}</pre>
    </div>
  </div>
</template>

<script>
import aupload from '@/components/aupload.vue'

export default {
  name: 'DebugUpload',
  components: {
    aupload
  },
  data() {
    return {
      uploadResult: '',
      testFile: null,
      debugInfo: '等待上传文件...'
    }
  },
  methods: {
    onDebugUpload(filePath) {
      console.log('调试上传成功:', filePath);
      this.uploadResult = filePath;
      this.$message.success('上传成功');
    },
    
    testFormData(event) {
      this.testFile = event.target.files[0];
      if (this.testFile) {
        this.debugInfo = `选择的文件:
名称: ${this.testFile.name}
大小: ${this.testFile.size} bytes
类型: ${this.testFile.type}
最后修改: ${new Date(this.testFile.lastModified).toLocaleString()}`;
      }
    },
    
    sendTestData() {
      if (!this.testFile) return;
      
      // 创建FormData
      const formData = new FormData();
      formData.append('upFile', this.testFile);
      formData.append('isBinary', 'true');
      formData.append('fileType', this.testFile.type || 'application/octet-stream');
      
      // 显示FormData内容
      let formDataContent = 'FormData内容:\n';
      for (let pair of formData.entries()) {
        if (pair[1] instanceof File) {
          formDataContent += `  ${pair[0]}: [File] ${pair[1].name} (${pair[1].size} bytes)\n`;
        } else {
          formDataContent += `  ${pair[0]}: ${pair[1]}\n`;
        }
      }
      
      this.debugInfo = formDataContent;
      
      // 发送请求
      this.$utils.http({
        data: formData,
        headers: {
          'token': this.$utils.store.getS('token'),
          'Content-Type': 'multipart/form-data',
        },
        success: (res) => {
          console.log('手动测试上传成功:', res);
          this.uploadResult = res.data.originFile;
          this.$message.success('手动测试上传成功');
        },
        fail: (error) => {
          console.error('手动测试上传失败:', error);
          this.$message.error('手动测试上传失败');
        }
      }, '/api/common/fileUpload');
    }
  }
}
</script>

<style scoped>
h2 {
  color: #1890ff;
  border-bottom: 2px solid #f0f0f0;
  padding-bottom: 10px;
}

h3 {
  color: #333;
  margin-bottom: 15px;
}

p {
  color: #666;
  font-size: 14px;
  margin-top: 10px;
  padding: 8px;
  background-color: #f9f9f9;
  border-radius: 4px;
  min-height: 20px;
}

pre {
  white-space: pre-wrap;
  word-wrap: break-word;
  font-size: 12px;
  max-height: 300px;
  overflow-y: auto;
}

button {
  margin-left: 10px;
  padding: 5px 15px;
  background: #1890ff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

button:disabled {
  background: #ccc;
  cursor: not-allowed;
}

input[type="file"] {
  margin-bottom: 10px;
}
</style>
