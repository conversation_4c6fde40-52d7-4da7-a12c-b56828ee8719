<template>
  <Layout selected="product" open="sub2">
    <a-breadcrumb style="margin: 16px 0">
      <a-breadcrumb-item><a href="/#/product/list"><a-icon type="shopping" /> 产品</a></a-breadcrumb-item>
      <a-breadcrumb-item><a-icon type="edit" /> 编辑</a-breadcrumb-item>
    </a-breadcrumb>

    <a-page-header
      style="border: 1px solid rgb(235, 237, 240);background:#fff;"
      title="产品"
      sub-title="编辑"
      @back="() => $router.go(-1)"
    />
    
    <br />

    <a-layout-content
      :style="{ background: '#fff', padding: '24px', margin: 0, minHeight: '280px' }"
    >

        <a-form-model
            ref="ruleForm"
            :model="form"
            :rules="rules"
          >
            <a-form-model-item label="名称" prop="product_name">
              <a-input
                v-model="form.product_name"
                placeholder="产品名称"
              />
            </a-form-model-item>


            <a-form-model-item label="封面" prop="product_img">
             <aupload func="getUploadImg" @getUploadImg="getUploadImg($event)" :sourceValue="form.product_img" :url="uploadAction" :upName="uploadFileName"></aupload>
            </a-form-model-item>
         

            <a-form-model-item label="详情" prop="product_details">
              <editor func="editorVal" @editorVal="editorVal($event)" :sourceValue="form.product_details" :imgUploadUrl="uploadAction" :imgUploadName="uploadFileName"></editor>
            </a-form-model-item>


          

            <a-row type="flex" justify="start">
              <a-col :span="12">
              <a-button type="primary" @click="onSubmit" block>
                    <a-icon type="check-circle" />提交
                </a-button>
              </a-col>
              <a-col :span="12">
                <a-button style="margin-left: 10px;" @click="resetForm" block>
                    <a-icon type="undo" />重置
                </a-button>
              </a-col>
            </a-row>
              
              
          
          </a-form-model>
        </a-layout-content>
  </Layout>
</template>
<script>
import Layout from "../Layout";
import editor   from "../../components/editor";
import aupload   from "../../components/aupload";
import auploadMutil from "../../components/auploadMutil";



export default {
  components: { Layout, editor, aupload, auploadMutil},
  data() {
    return {
      //表单
      form: {
        product_name   : '',
        cover  : '',
        product_info  : '',
        
      }, 
      //规则
       rules: {
        product_name: [
          { required: true, message: '请输入名称', trigger: 'blur' },
        ],
        cover: [
          { required: true, message: '请上传封面', trigger: 'blur' },
        ],
        product_info: [
          { required: true, message: '请输入详情', trigger: 'blur' },
        ]
      },
      id : 0,

      uploadAction:"/admin/upload",
      uploadFileName:"file",
    };
  },

  created: function () {
    let self = this;

    if(this.$route.query.id != undefined) {
      self.id = this.$route.query.id;

       let editData = self.$utils.store.get('editData');
       
       self.form = editData;
    }

  },
  mounted:function () {
    
  },

  methods: {
    onSubmit() {
      let self = this;
      self.$refs.ruleForm.validate(valid => {
        
        if (!valid) {
          return false;
        }

        //console.log(self.form);
        self.edit();
      });
    },
    resetForm() {
      this.$refs.ruleForm.resetFields();
    },

    edit() {
        let self = this;
        let id   = self.id;

     
        let data   = self.form;

        if(self.id != 0) {
           data.id = id;
        }

        self.$utils.http({
            data : data,
            success:(res)=> {
              self.$message.success(res.msg || res.message || '操作成功');
              self.$router.go(-1);
            },
            fail: (err) => {
              console.error('提交失败:', err);
            }

          }, '/admin/product/edit')
      
    },

     //接受富文本值
    editorVal(html) {
      this.form.product_info = html;
    },
    
    //接受示例图值
    getUploadImg(val) {

      //console.log(val);
       this.form.cover = val;
    },

  }
  
};
</script>

<style>
#components-layout-demo-top-side-2 .logo {
  width: 120px;
  height: 31px;
  background: rgba(255, 255, 255, 0.2);
  margin: 16px 28px 16px 0;
  float: left;
}
</style>
