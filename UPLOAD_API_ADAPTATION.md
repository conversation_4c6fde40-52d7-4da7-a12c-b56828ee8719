# 上传组件接口适配说明

## 接口响应格式变更

### 新的接口响应格式
```json
{
  "code": 200,
  "msg": "成功",
  "data": [
    {
      "fileName": "7e26375180f42ae94f9b7ed3b377ce0d.jpg",
      "filePath": "2025/07/27/7e26375180f42ae94f9b7ed3b377ce0d.jpg",
      "fileSize": 38237,
      "fileType": "image/jpeg",
      "fullPath": "uploads/2025/07/27/7e26375180f42ae94f9b7ed3b377ce0d.jpg",
      "originalName": "wechat_2025-06-09_023008_377.jpg"
    }
  ]
}
```

### 字段映射关系

| 新接口字段 | 说明 | 组件内部使用 |
|-----------|------|-------------|
| fileName | 服务器生成的文件名 | 存储为 fileName |
| filePath | 相对路径 | 用于回调和 originFile |
| fileSize | 文件大小（字节） | 映射到 size |
| fileType | MIME 类型 | 映射到 type |
| fullPath | 完整相对路径 | 用于构建完整 URL |
| originalName | 原始文件名 | 映射到 name |

## 组件适配变更

### 1. 响应数据处理
所有组件都已适配数组格式响应：
```javascript
// 适配新的接口响应格式 - data 是数组
let uploadedFile = Array.isArray(data) ? data[0] : data;
```

### 2. URL 构建
完整的文件访问 URL 构建：
```javascript
url: self.$utils.getHttp() + '/' + uploadedFile.fullPath
```

### 3. 文件信息映射
```javascript
let fileList = {
  uid: self.fileNum,
  name: uploadedFile.originalName || option.file.name,  // 使用原始文件名
  status: 'done',
  url: self.$utils.getHttp() + '/' + uploadedFile.fullPath,  // 完整URL
  type: uploadedFile.fileType || option.file.type,      // 服务器返回的类型
  size: uploadedFile.fileSize || option.file.size,     // 服务器返回的大小
  fileName: uploadedFile.fileName,                      // 服务器文件名
  filePath: uploadedFile.filePath,                      // 相对路径
  fullPath: uploadedFile.fullPath                       // 完整路径
};
```

### 4. 回调数据
父组件回调现在传递 `filePath`：
```javascript
self.$emit(self.func, uploadedFile.filePath);
```

## 向后兼容性

### 兼容旧格式
如果服务器返回的不是数组格式，组件会自动适配：
```javascript
let uploadedFile = Array.isArray(data) ? data[0] : data;
```

### 字段回退
如果新字段不存在，会回退到原始值：
```javascript
name: uploadedFile.originalName || option.file.name,
type: uploadedFile.fileType || option.file.type,
size: uploadedFile.fileSize || option.file.size
```

## 使用示例

### 父组件接收回调
```javascript
methods: {
  handleUpload(filePath) {
    console.log('上传成功，文件路径:', filePath);
    // filePath 格式: "2025/07/27/7e26375180f42ae94f9b7ed3b377ce0d.jpg"
    
    // 如果需要完整URL，可以这样构建：
    const fullUrl = this.$utils.getHttp() + '/uploads/' + filePath;
  }
}
```

### 组件使用
```vue
<template>
  <!-- 所有组件使用方式不变 -->
  <aupload 
    :func="'handleUpload'"
    :supportBinary="true"
    @handleUpload="handleUpload"
  />
</template>
```

## 调试信息

上传成功后，控制台会显示完整的文件信息：
```javascript
console.log('上传文件信息:', {
  fileName: "7e26375180f42ae94f9b7ed3b377ce0d.jpg",
  filePath: "2025/07/27/7e26375180f42ae94f9b7ed3b377ce0d.jpg",
  fileSize: 38237,
  fileType: "image/jpeg",
  fullPath: "uploads/2025/07/27/7e26375180f42ae94f9b7ed3b377ce0d.jpg",
  originalName: "wechat_2025-06-09_023008_377.jpg"
});
```

## 注意事项

1. **消息字段变更**: `res.message` 改为 `res.msg`
2. **数据结构**: `res.data` 现在是数组格式
3. **URL构建**: 需要手动拼接完整的访问URL
4. **文件路径**: 回调返回的是相对路径，不是完整URL
5. **文件名**: 显示名称使用 `originalName`，存储使用 `fileName`

## 测试建议

1. 测试单文件上传的响应处理
2. 测试多文件上传的响应处理
3. 验证文件预览功能是否正常
4. 检查回调数据格式是否正确
5. 确认文件下载链接是否有效

所有组件现在都已适配新的接口格式，保持了向后兼容性，可以安全使用。
