# 富文本编辑器图片上传适配说明

## 修改概述

已成功修改 `src/components/editor.vue` 富文本编辑器组件，使其适配新的图片上传接口响应格式。

## 接口响应格式变更

### 新的接口响应格式
```json
{
  "code": 200,
  "msg": "成功",
  "data": [
    {
      "fileName": "7e26375180f42ae94f9b7ed3b377ce0d.jpg",
      "filePath": "2025/07/27/7e26375180f42ae94f9b7ed3b377ce0d.jpg",
      "fileSize": 38237,
      "fileType": "image/jpeg",
      "fullPath": "uploads/2025/07/27/7e26375180f42ae94f9b7ed3b377ce0d.jpg",
      "originalName": "wechat_2025-06-09_023008_377.jpg"
    }
  ]
}
```

### 旧的接口响应格式（兼容）
```json
{
  "code": 200,
  "message": "成功",
  "data": {
    "httpFile": "http://example.com/uploads/image.jpg",
    "originFile": "uploads/image.jpg"
  }
}
```

## 主要修改内容

### 1. 消息字段适配
```javascript
// 支持新旧两种消息字段
self.$message.success(result.msg || result.message || '上传成功');
self.$message.error(result.msg || result.message || '上传失败');
self.$message.error(response.msg || response.message || '上传出错');
```

### 2. 图片URL构建逻辑
```javascript
customInsert: function (insertImg, result, editor) {
    console.log('Editor upload result:', result);
    
    // 适配新的接口响应格式
    let data = result.data;
    let uploadedFile = Array.isArray(data) ? data[0] : data;
    
    // 构建完整的图片URL
    let imageUrl;
    if (uploadedFile.fullPath) {
        // 新接口格式：使用 fullPath
        imageUrl = self.$utils.getHttp() + '/' + uploadedFile.fullPath;
    } else if (uploadedFile.httpFile) {
        // 兼容旧接口格式：使用 httpFile
        imageUrl = uploadedFile.httpFile;
    } else {
        console.error('无法获取图片URL，响应数据:', result);
        return;
    }
    
    console.log('插入图片URL:', imageUrl);
    insertImg(imageUrl);
}
```

### 3. 数组格式适配
- 自动检测 `result.data` 是数组还是对象
- 如果是数组，取第一个元素
- 如果是对象，直接使用

### 4. 向后兼容性
- 支持新的 `fullPath` 字段
- 兼容旧的 `httpFile` 字段
- 支持 `msg` 和 `message` 两种消息字段
- 自动适配数组和对象两种数据格式

## 编辑器配置

### 上传配置保持不变
```javascript
// 配置服务器端地址
editor.customConfig.uploadImgServer = self.$utils.getHttp()+'/api/common/imgUpload'
editor.customConfig.uploadFileName = 'upFile'

editor.customConfig.uploadImgHeaders = {
   'token': self.$utils.store.getS('token')
}
```

### 菜单配置
编辑器支持以下功能：
- 标题、粗体、字号、字体
- 斜体、下划线、删除线
- 文字颜色、背景颜色
- 链接、列表、对齐方式
- 引用、表情、**图片上传**
- 表格、撤销、重复

## 使用方式

### 基本使用
```vue
<template>
  <editor 
    :func="'handleEditorChange'"
    :sourceValue="initialContent"
    @handleEditorChange="onEditorChange"
  />
</template>

<script>
import editor from '@/components/editor.vue'

export default {
  components: { editor },
  data() {
    return {
      editorContent: '',
      initialContent: '<p>初始内容</p>'
    }
  },
  methods: {
    onEditorChange(html) {
      console.log('编辑器内容:', html);
      this.editorContent = html;
    }
  }
}
</script>
```

### 图片上传流程
1. 用户点击编辑器工具栏中的图片按钮
2. 选择图片文件
3. 文件自动上传到 `/api/common/imgUpload`
4. 服务器返回新格式的响应数据
5. 编辑器解析响应，构建完整图片URL
6. 图片自动插入到编辑器中

## 调试功能

### 控制台日志
上传过程中会输出详细日志：
```javascript
// 上传响应数据
Editor upload result: {code: 200, msg: "成功", data: [...]}

// 构建的图片URL
插入图片URL: http://localhost:8080/uploads/2025/07/27/xxx.jpg
```

### 错误处理
- 上传前显示loading提示
- 上传成功显示成功消息
- 上传失败显示错误消息
- 网络错误显示详细错误信息

## 测试建议

### 功能测试
1. **图片上传测试**
   - 测试JPG、PNG、GIF等格式
   - 测试不同大小的图片
   - 测试上传失败的情况

2. **兼容性测试**
   - 测试新接口格式的响应
   - 测试旧接口格式的响应（如果还在使用）
   - 测试数组和对象两种数据格式

3. **界面测试**
   - 验证图片正确显示在编辑器中
   - 验证图片可以正常预览
   - 验证编辑器其他功能不受影响

### 使用测试页面
创建了 `src/views/test-editor.vue` 测试页面，包含：
- 完整的编辑器功能演示
- 实时内容预览
- HTML源码查看
- 详细的测试说明
- 操作按钮和调试信息

## 注意事项

1. **图片URL构建**
   - 新格式使用 `fullPath` 字段
   - 需要手动拼接完整URL：`baseUrl + '/' + fullPath`

2. **响应格式检测**
   - 自动检测数组/对象格式
   - 优先使用新格式字段
   - 回退到旧格式字段

3. **错误处理**
   - 增强了错误信息显示
   - 添加了详细的调试日志
   - 提供了更好的用户反馈

4. **性能考虑**
   - 保持了原有的上传性能
   - 添加的适配逻辑开销很小
   - 不影响编辑器其他功能

## 向后兼容性

✅ **完全向后兼容**
- 现有使用编辑器的页面无需修改
- 自动适配新旧接口格式
- 保持所有原有功能不变
- 只是增强了图片上传的适配能力

富文本编辑器现在已经完全适配新的图片上传接口，可以正常使用图片上传功能了！
